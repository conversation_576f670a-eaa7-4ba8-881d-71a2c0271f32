"use client"
import React, { useState, useEffect } from "react";
import ReactDOM from "react-dom";
import { message, Drawer, Button } from "antd";
import { MenuFoldOutlined } from "@ant-design/icons";
import { requestFlowApproval, requestFlowAuditTurn, requestOrderDetail } from "@/request/api";
import CommentSection from "../CommentSection";
import styled from "styled-components";
import Cookies from "js-cookie";
import {
  X,
  User,
  FileText,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertCircle,
  UserCheck,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";

// 自定义UI组件样式 - 模仿shadcn/ui
const CustomButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 8px 16px;
  height: 36px;
  
  &.variant-default {
    background-color: #0f172a;
    color: #f8fafc;
    &:hover {
      background-color: #1e293b;
    }
  }
  
  &.variant-outline {
    border: 1px solid #e2e8f0;
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }
  
  &.variant-ghost {
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }
  
  &.variant-destructive {
    background-color: #dc2626;
    color: #f8fafc;
    &:hover {
      background-color: #b91c1c;
    }
  }
  
  &.size-sm {
    height: 32px;
    padding: 6px 12px;
    font-size: 13px;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Card = styled.div`
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
`;

const CardHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 16px;
`;

const CardTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  color: #1f2937;
`;

const CardContent = styled.div`
  padding: 0 24px 24px 24px;
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid transparent;
  
  &.bg-green-100 {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }
  
  &.bg-blue-100 {
    background-color: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;
  }
  
  &.bg-orange-100 {
    background-color: #fed7aa;
    color: #c2410c;
    border-color: #fdba74;
  }
  
  &.bg-red-100 {
    background-color: #fee2e2;
    color: #dc2626;
    border-color: #fecaca;
  }
  
  &.bg-yellow-100 {
    background-color: #fef3c7;
    color: #d97706;
    border-color: #fde68a;
  }
  
  &.bg-gray-100 {
    background-color: #f3f4f6;
    color: #374151;
    border-color: #e5e7eb;
  }
`;

const CustomTextarea = styled.textarea`
  display: flex;
  min-height: 80px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 12px;
  font-size: 14px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomInput = styled.input`
  display: flex;
  height: 36px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 8px 12px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomLabel = styled.label`
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  color: #374151;
`;

// Dialog组件样式 - 设置极高z-index确保在所有元素之上
const DialogOverlay = styled.div`
  position: fixed;
  inset: 0;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DialogContent = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 32rem;
  max-height: 85vh;
  overflow-y: auto;
`;

const DialogHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 16px;
`;

const DialogTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DialogFooter = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 24px;
  padding-top: 16px;
  
  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: flex-end;
  }
`;

export default function OrderDetailDrawer({ orderID, orderType, visible, onClose, title }) {
  // 状态管理 - 保持原有的所有状态变量
  const [auditButtonVisible, setAuditButtonVisible] = useState(false);
  const [turnAuditButtonVisible, setTurnAuditButtonVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const [remark, setRemark] = useState("");
  const [stageInfos, setStageInfos] = useState([]);
  const [orderInfo, setOrderInfo] = useState({ info: {} });

  const [comment, setComment] = useState("");
  const [showOwnerChangeModal, setShowOwnerChangeModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [newOwnerEmail, setNewOwnerEmail] = useState("");
  const [approvalAction, setApprovalAction] = useState("approve");


  
  const cnMap = {
    "common": {
      "title": [3, "标题"],
      "ops_audit_email": [3, "运维审批人"],
      "apply_msg": [6, "申请理由"],
    },
    "sql_audit_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "sql_audit_file_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "server_jump_impower": {
      "server_ip": [6, "申请登陆权限IP"],
      "apply_msg": [6, "申请理由"],
    },
    "pointed_approver": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "cdn_create_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "domain_resolve_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_new_refresh": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_view": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    }
  };

  // 工具函数 - 获取中文映射
  const getCnMap = (orderType) => {
    if (orderType in cnMap) {
      return cnMap[orderType];
    }
    return {
      "apply_msg": [6, "申请理由"]
    };
  };

  // 数据获取函数
  const fetchOrderDetail = async (orderID) => {
    try {
      const userEmail = Cookies.get("user_email");
      const data = await requestOrderDetail({ order_id: orderID });

      if (data === null) {
        return;
      }

      const stageInfosData = data.stage_infos;
      const orderInfoData = data.order_info;

      // 初始化按钮显示状态
      let auditButtonVisibleState = false;
      let turnAuditButtonVisibleState = false;

      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0) {
        auditButtonVisibleState = true;
      }
      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0 && orderInfoData.current_stage < orderInfoData.total_stage_num) {
        turnAuditButtonVisibleState = true;
      }

      // 解析详情字段
      orderInfoData.info = JSON.parse(orderInfoData.info);
      orderInfoData.info["apply_msg"] = orderInfoData.apply_msg;

      setAuditButtonVisible(auditButtonVisibleState);
      setTurnAuditButtonVisible(turnAuditButtonVisibleState);
      setStageInfos(stageInfosData);
      setOrderInfo(orderInfoData);
      setDrawerVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  // useEffect替代componentDidMount和componentDidUpdate
  useEffect(() => {
    const isVisible = visible === true;
    if (isVisible && orderID) {
      fetchOrderDetail(orderID);
    }
  }, [visible, orderID]);

  // 状态图标和颜色函数
  const getStatusIcon = (status) => {
    switch (status) {
      case "已完成":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "处理中":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "待审批":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "已完成":
        return "bg-green-100";
      case "处理中":
        return "bg-blue-100";
      case "待审批":
        return "bg-orange-100";
      default:
        return "bg-gray-100";
    }
  };

  // 根据工单结果获取状态显示
  const getOrderStatus = (result) => {
    switch (result) {
      case 0:
        return { text: "进行中", color: "bg-blue-100" };
      case 1:
        return { text: "完成", color: "bg-green-100" };
      case 2:
        return { text: "驳回", color: "bg-red-100" };
      case 3:
        return { text: "失败", color: "bg-red-100" };
      case 4:
        return { text: "超时", color: "bg-yellow-100" };
      default:
        return { text: "未知", color: "bg-gray-100" };
    }
  };

  // 获取状态图标
  const getStatusIconForBadge = (result) => {
    switch (result) {
      case 0:
        return <Clock style={{ width: '12px', height: '12px' }} />;
      case 1:
        return <CheckCircle style={{ width: '12px', height: '12px' }} />;
      case 2:
        return <X style={{ width: '12px', height: '12px' }} />;
      default:
        return <Clock style={{ width: '12px', height: '12px' }} />;
    }
  };

  // 获取阶段状态显示信息
  const getStageStatusInfo = (stageResult) => {
    switch (stageResult) {
      case 0:
        return { text: '进行中', color: '#3b82f6' }; // 蓝色
      case 1:
        return { text: '已完成', color: '#10b981' }; // 绿色
      case 2:
        return { text: '已驳回', color: '#ef4444' }; // 红色
      case 3:
        return { text: '失败', color: '#dc2626' }; // 深红色
      case 4:
        return { text: '超时', color: '#f59e0b' }; // 橙色
      default:
        return { text: '未知', color: '#6b7280' }; // 灰色
    }
  };



  // 事件处理函数 - 保持原有的API调用逻辑
  const handleOwnerChange = async () => {
    try {
      await requestFlowAuditTurn({
        order_id: orderID,
        audit_email: newOwnerEmail.trim()
      });
      message.success("审批人变更成功");
      setShowOwnerChangeModal(false);
      setNewOwnerEmail("");
      // 重新获取数据
      fetchOrderDetail(orderID);
    } catch (err) {
      message.error("审批人变更失败");
      console.log(err);
    }
  };

  const handleApproval = async () => {
    try {
      const result = approvalAction === "approve" ? 1 : 2;
      await requestFlowApproval({
        order_id: orderID,
        result: result,
        remark: ""
      });
      message.success(`审批${approvalAction === "approve" ? "通过" : "驳回"}成功`);
      setShowApprovalModal(false);
      setApprovalAction("approve");
      // 重新获取数据
      fetchOrderDetail(orderID);
    } catch (err) {
      message.error(`审批${approvalAction === "approve" ? "通过" : "驳回"}失败`);
      console.log(err);
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      setDrawerVisible(false);
    }
  };

  // 如果不可见，不渲染
  const isOpen = visible !== undefined ? visible : drawerVisible;

  return (
    <>
      {/* 只有在没有外部控制时才显示按钮 */}
      {visible === undefined && (
        <Button
          type="primary"
          size="small"
          onClick={() => {
            if (orderID) {
              fetchOrderDetail(orderID);
            }
          }}
          icon={<MenuFoldOutlined />}
          style={{
            backgroundColor: '#2563eb',
            borderColor: '#2563eb',
            color: 'white'
          }}
        >
          {title || "详情"}
        </Button>
      )}

      {/* 抽屉内容 - 使用Antd的Drawer组件 */}
      <Drawer
        width={800}
        placement="right"
        closable={false}
        onClose={handleClose}
        open={isOpen && !!orderInfo.order_id}
        zIndex={1000} // 设置Drawer的z-index
        title={
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%'
          }}>
            <div style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              审批流程跟踪
            </div>
            <CustomButton
              className="variant-ghost"
              onClick={handleClose}
              style={{
                padding: '4px',
                minWidth: 'auto',
                height: 'auto'
              }}
            >
              <X style={{ width: '16px', height: '16px' }} />
            </CustomButton>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: '16px'
          }
        }}
      >
          <div style={{ padding: '24px' }}>
            {/* 操作按钮 - 只有在有按钮时才显示容器 */}
            {(turnAuditButtonVisible || auditButtonVisible) && (
              <div style={{ display: 'flex', gap: '12px', marginBottom: '16px' }}>
                {turnAuditButtonVisible && (
                  <CustomButton
                    className="variant-outline size-sm"
                    onClick={() => setShowOwnerChangeModal(true)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                  >
                    <UserCheck style={{ width: '16px', height: '16px' }} />
                    负责人变更
                  </CustomButton>
                )}
                {auditButtonVisible && (
                  <CustomButton
                    className="variant-outline size-sm"
                    onClick={() => setShowApprovalModal(true)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                  >
                    <ThumbsUp style={{ width: '16px', height: '16px' }} />
                    审批
                  </CustomButton>
                )}
              </div>
            )}

            {/* 流程跟踪 */}
            <Card style={{ marginBottom: '16px' }}>
              <CardHeader>
                <CardTitle>流程状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {/* 审批步骤 */}
                  {stageInfos.map((stage, index) => {
                    const stageNum = stage.stage_num;
                    const isActive = stageNum <= orderInfo.current_stage; // 小于等于current_stage的阶段是激活的
                    const stageStatusInfo = getStageStatusInfo(stage.stage_result);

                    let stepColor = '#e5e7eb'; // 默认灰色
                    let stepIcon = <Clock style={{ width: '16px', height: '16px', color: '#6b7280' }} />;
                    let textColor = '#9ca3af'; // 灰色文字

                    if (isActive) {
                      // 激活的阶段根据stage_result显示不同颜色
                      stepColor = stageStatusInfo.color;
                      textColor = '#1f2937'; // 黑色文字

                      switch (stage.stage_result) {
                        case 0: // 进行中
                          stepIcon = <Clock style={{ width: '16px', height: '16px', color: 'white' }} />;
                          break;
                        case 1: // 已完成
                          stepIcon = <CheckCircle style={{ width: '16px', height: '16px', color: 'white' }} />;
                          break;
                        case 2: // 已驳回
                        case 3: // 失败
                          stepIcon = <X style={{ width: '16px', height: '16px', color: 'white' }} />;
                          break;
                        case 4: // 超时
                          stepIcon = <AlertCircle style={{ width: '16px', height: '16px', color: 'white' }} />;
                          break;
                        default:
                          stepIcon = <Clock style={{ width: '16px', height: '16px', color: 'white' }} />;
                      }
                    }

                    return (
                      <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <div style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          backgroundColor: stepColor,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          {stepIcon}
                        </div>
                        <div style={{ flex: 1 }}>
                          <div style={{ fontSize: '14px', fontWeight: '500', color: textColor }}>
                            {stage.stage_name}
                          </div>
                          <div style={{ fontSize: '12px', color: isActive ? '#6b7280' : '#9ca3af' }}>
                            {stage.stage_operator}
                          </div>
                        </div>
                        <div style={{
                          fontSize: '12px',
                          color: isActive ? stageStatusInfo.color : '#9ca3af',
                          fontWeight: '500'
                        }}>
                          {isActive ? stageStatusInfo.text : '待处理'}
                        </div>
                      </div>
                    );
                  })}

                  {/* Done步骤 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: orderInfo.result === 1 ? '#10b981' : '#e5e7eb',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <CheckCircle style={{
                        width: '16px',
                        height: '16px',
                        color: orderInfo.result === 1 ? 'white' : '#6b7280'
                      }} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>Done</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 工单信息 */}
            <Card style={{ marginBottom: '24px' }}>
              <CardHeader>
                <CardTitle>工单信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>工单ID</div>
                    <div style={{ fontSize: '14px', color: '#1f2937' }}>{orderInfo.order_id}</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>工单类型</div>
                    <div style={{ fontSize: '14px', color: '#1f2937' }}>{orderInfo.order_type_name || orderInfo.order_type}</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>申请人</div>
                    <div style={{ fontSize: '14px', color: '#1f2937' }}>{orderInfo.proposer_email}</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px', fontWeight: '500' }}>状态</div>
                    <div style={{ marginTop: '4px' }}>
                      <Badge className={`${getOrderStatus(orderInfo.result).color} border`} style={{
                        fontSize: '12px',
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}>
                        {getStatusIconForBadge(orderInfo.result)}
                        {getOrderStatus(orderInfo.result).text}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 工单详情 */}
            <Card style={{ marginBottom: '24px' }}>
              <CardHeader>
                <CardTitle>工单详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px', fontWeight: '500' }}>申请理由</div>
                  <div style={{
                    padding: '12px',
                    backgroundColor: '#f9fafb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    lineHeight: '1.6',
                    color: '#374151',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {orderInfo.apply_msg || '暂无申请理由'}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 评论区域 */}
            <Card style={{ marginBottom: '24px' }}>
              <CardHeader>
                <CardTitle>评论</CardTitle>
              </CardHeader>
              <CardContent>
                {orderInfo.order_id && (
                  <CommentSection
                    orderId={orderInfo.order_id}
                    canComment={orderInfo.result === 0}
                    currentStage={orderInfo.current_stage}
                  />
                )}
              </CardContent>
            </Card>
          </div>
        </Drawer>

      {/* 负责人变更弹窗 - 使用Portal渲染到body */}
      {showOwnerChangeModal && ReactDOM.createPortal(
        <DialogOverlay onClick={() => setShowOwnerChangeModal(false)}>
          <DialogContent onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>
                <UserCheck className="h-5 w-5 text-blue-600" />
                负责人变更
              </DialogTitle>
              <button
                onClick={() => setShowOwnerChangeModal(false)}
                style={{
                  position: 'absolute',
                  right: '24px',
                  top: '24px',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: '4px',
                  borderRadius: '4px',
                  color: '#6b7280'
                }}
              >
                <X style={{ width: '20px', height: '20px' }} />
              </button>
            </DialogHeader>
            <div style={{ padding: '0 24px', marginBottom: '24px' }}>
              {/* 当前负责人信息卡片 */}
              <div style={{
                backgroundColor: '#eff6ff',
                border: '1px solid #dbeafe',
                borderRadius: '12px',
                padding: '16px',
                marginBottom: '24px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    backgroundColor: '#dbeafe',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <User style={{ width: '20px', height: '20px', color: '#3b82f6' }} />
                  </div>
                  <div>
                    <div style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '4px' }}>
                      当前负责人
                    </div>
                    <div style={{ fontSize: '14px', color: '#6b7280' }}>
                      {stageInfos.find(stage => stage.stage_num === orderInfo.current_stage)?.audit_email || "李四"}
                    </div>
                  </div>
                </div>
              </div>

              {/* 新负责人邮箱输入 */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  新负责人邮箱 <span style={{ color: '#ef4444' }}>*</span>
                </label>
                <input
                  type="email"
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: '#ffffff'
                  }}
                  placeholder="请输入新负责人的邮箱地址"
                  value={newOwnerEmail}
                  onChange={(e) => setNewOwnerEmail(e.target.value)}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                />
                <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '8px' }}>
                  系统将自动发送通知邮件给新负责人
                </p>
              </div>
            </div>
            <div style={{
              display: 'flex',
              gap: '12px',
              padding: '24px',
              paddingTop: '0'
            }}>
              <CustomButton
                className="variant-outline"
                onClick={() => setShowOwnerChangeModal(false)}
                style={{
                  flex: 1,
                  padding: '12px 24px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                取消
              </CustomButton>
              <CustomButton
                onClick={handleOwnerChange}
                disabled={!newOwnerEmail}
                style={{
                  flex: 1,
                  padding: '12px 24px',
                  fontSize: '14px',
                  fontWeight: '500',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
              >
                <UserCheck style={{ width: '16px', height: '16px' }} />
                确认变更
              </CustomButton>
            </div>
          </DialogContent>
        </DialogOverlay>,
        document.body
      )}

      {/* 审批弹窗 - 使用Portal渲染到body */}
      {showApprovalModal && ReactDOM.createPortal(
        <DialogOverlay onClick={() => setShowApprovalModal(false)}>
          <DialogContent onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>
                {approvalAction === "approve" ? (
                  <ThumbsUp className="h-5 w-5 text-blue-600" />
                ) : (
                  <ThumbsDown className="h-5 w-5 text-red-600" />
                )}
                审批操作
              </DialogTitle>
              <button
                onClick={() => setShowApprovalModal(false)}
                style={{
                  position: 'absolute',
                  right: '24px',
                  top: '24px',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: '4px',
                  borderRadius: '4px',
                  color: '#6b7280'
                }}
              >
                <X style={{ width: '20px', height: '20px' }} />
              </button>
            </DialogHeader>
            <div style={{ padding: '0 24px', marginBottom: '24px' }}>
              {/* 工单信息卡片 */}
              <div style={{
                backgroundColor: '#f9fafb',
                border: '1px solid #e5e7eb',
                borderRadius: '12px',
                padding: '16px',
                marginBottom: '24px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    backgroundColor: '#e5e7eb',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <FileText style={{ width: '20px', height: '20px', color: '#6b7280' }} />
                  </div>
                  <div>
                    <div style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '4px' }}>
                      工单信息
                    </div>
                    <div style={{ fontSize: '14px', color: '#6b7280' }}>
                      数据库维护工单 - 数据库
                    </div>
                  </div>
                </div>
              </div>

              {/* 审批决定 */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '12px'
                }}>
                  审批决定
                </label>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                  <button
                    onClick={() => setApprovalAction("approve")}
                    style={{
                      padding: '16px',
                      border: approvalAction === "approve" ? '2px solid #3b82f6' : '2px solid #e5e7eb',
                      borderRadius: '8px',
                      backgroundColor: approvalAction === "approve" ? '#3b82f6' : '#ffffff',
                      color: approvalAction === "approve" ? '#ffffff' : '#374151',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                      transition: 'all 0.2s'
                    }}
                  >
                    <ThumbsUp style={{ width: '16px', height: '16px' }} />
                    同意
                  </button>
                  <button
                    onClick={() => setApprovalAction("reject")}
                    style={{
                      padding: '16px',
                      border: approvalAction === "reject" ? '2px solid #ef4444' : '2px solid #e5e7eb',
                      borderRadius: '8px',
                      backgroundColor: approvalAction === "reject" ? '#ffffff' : '#ffffff',
                      color: approvalAction === "reject" ? '#ef4444' : '#374151',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                      transition: 'all 0.2s'
                    }}
                  >
                    <ThumbsDown style={{ width: '16px', height: '16px' }} />
                    驳回
                  </button>
                </div>
              </div>
            </div>
            <div style={{
              display: 'flex',
              gap: '12px',
              padding: '24px',
              paddingTop: '0'
            }}>
              <CustomButton
                className="variant-outline"
                onClick={() => setShowApprovalModal(false)}
                style={{
                  flex: 1,
                  padding: '12px 24px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                取消
              </CustomButton>
              <CustomButton
                onClick={handleApproval}
                style={{
                  flex: 1,
                  padding: '12px 24px',
                  fontSize: '14px',
                  fontWeight: '500',
                  backgroundColor: approvalAction === "approve" ? '#3b82f6' : '#ef4444',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
              >
                {approvalAction === "approve" ? (
                  <>
                    <ThumbsUp style={{ width: '16px', height: '16px' }} />
                    确认同意
                  </>
                ) : (
                  <>
                    <ThumbsDown style={{ width: '16px', height: '16px' }} />
                    确认驳回
                  </>
                )}
              </CustomButton>
            </div>
          </DialogContent>
        </DialogOverlay>,
        document.body
      )}
    </>
  );
}
