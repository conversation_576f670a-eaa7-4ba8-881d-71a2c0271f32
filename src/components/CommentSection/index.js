import React, { Component } from 'react';
import { message } from 'antd';
import { fetchComments, postComment } from '../../services/commentService';
import CommentList from './CommentList';
import CommentInput from './CommentInput';
import './styles.css';

/**
 * CommentSection - 评论区容器组件
 * 管理评论列表的状态、分页、加载状态，并处理用户交互
 */
export default class CommentSection extends Component {
  constructor(props) {
    super(props);
    this.state = {
      comments: [],           // 评论列表
      pagination: { cursor: 0, hasMore: true, nextCursor: null },  // 游标分页状态对象
      isLoading: false,      // 列表加载状态
      isSubmitting: false,   // 提交状态
      error: null,           // 错误信息
    };
    this.pollingTimer = null;  // 轮询定时器
  }

  componentDidMount() {
    // 只有当orderId有效时才加载评论和启动轮询
    if (this.props.orderId) {
      this.loadComments();
      this.startPolling();
    }
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  }

  componentDidUpdate(prevProps) {
    // 当orderId从无效变为有效时，开始加载评论
    if (!prevProps.orderId && this.props.orderId) {
      this.loadComments();
      this.startPolling();
    }
    // 当orderId发生变化时，重新加载评论
    else if (prevProps.orderId !== this.props.orderId && this.props.orderId) {
      this.stopPolling();
      this.setState({
        comments: [],
        pagination: { cursor: 0, hasMore: true, nextCursor: null },
        error: null
      });
      this.loadComments();
      this.startPolling();
    }
    // 当orderId变为无效时，停止轮询
    else if (prevProps.orderId && !this.props.orderId) {
      this.stopPolling();
    }
  }

  componentWillUnmount() {
    this.stopPolling();
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
  }

  /**
   * 处理页面可见性变化 - 智能启停轮询
   */
  handleVisibilityChange = () => {
    if (document.hidden) {
      this.stopPolling();
    } else {
      this.startPolling();
    }
  };

  /**
   * 启动轮询
   */
  startPolling = () => {
    if (this.pollingTimer) return; // 防止重复启动

    this.pollingTimer = setInterval(() => {
      this.pollForNewComments();
    }, 10000); // 每10秒轮询一次
  };

  /**
   * 停止轮询
   */
  stopPolling = () => {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;
    }
  };

  /**
   * 轮询检查新评论
   */
  pollForNewComments = async () => {
    // 验证orderId是否有效
    if (!this.props.orderId) {
      console.warn('CommentSection: orderId为空，停止轮询');
      this.stopPolling();
      return;
    }
    
    if (this.state.isLoading || this.state.isSubmitting) return;

    try {
      const response = await fetchComments(this.props.orderId, 0, 10);
      const { data } = response.data;
      
      if (data && data.length > 0) {
        // 检查是否有新评论，通过对比评论ID集合
        const currentCommentIds = new Set(this.state.comments.map(c => String(c.id)));
        const newComments = data.filter(comment => !currentCommentIds.has(String(comment.id)));
        
        // 如果有新评论，添加到列表中
        if (newComments.length > 0) {
          this.setState(prevState => ({
            comments: this.sortCommentsByServerTime([...prevState.comments, ...newComments])
          }));
        }
      }
    } catch (error) {
      console.error('轮询检查新评论失败:', error);
      // 轮询失败不显示错误提示，避免打扰用户
    }
  };

  /**
   * 按服务器时间戳排序评论
   */
  sortCommentsByServerTime = (comments) => {
    return comments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  };

  /**
   * 加载评论列表
   */
  loadComments = async (cursor = 0, isLoadMore = false) => {
    // 验证orderId是否有效
    if (!this.props.orderId) {
      console.warn('CommentSection: orderId为空，无法加载评论');
      return;
    }
    
    if (this.state.isLoading) return;
    
    this.setState({ isLoading: true, error: null });
    
    try {
      const response = await fetchComments(this.props.orderId, cursor, 10);
      const { data, meta } = response.data;
      
      if (!isLoadMore) {
        // 首次加载或刷新 - 显示最新评论
        this.setState({
          comments: this.sortCommentsByServerTime(data),
          pagination: { 
            cursor: meta.next_cursor || 0, 
            hasMore: meta.has_more,
            nextCursor: meta.next_cursor 
          },
          isLoading: false,
        });
      } else {
        // 加载更多历史评论 - 将更早的评论添加到列表顶部
        this.setState({
          comments: this.sortCommentsByServerTime([...data, ...this.state.comments]),
          pagination: { 
            cursor: meta.next_cursor || 0, 
            hasMore: meta.has_more,
            nextCursor: meta.next_cursor 
          },
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('加载评论失败:', error);
      this.setState({ 
        error: '加载评论失败，请稍后重试',
        isLoading: false 
      });
    }
  };

  /**
   * 处理发表评论
   */
  handlePostComment = async (content) => {
    // 验证orderId是否有效
    if (!this.props.orderId) {
      message.error('工单信息异常，无法发表评论');
      return;
    }
    
    if (!content.trim()) {
      message.warning('评论内容不能为空');
      return;
    }

    this.setState({ isSubmitting: true });
    
    // 创建乐观更新的评论对象
    const optimisticComment = {
      id: `optimistic-${Date.now()}`,
      content: content.trim(),
      created_at: new Date().toISOString(),
      author: {
        id: 'current-user',
        name: '我' // 临时显示，实际应该从用户上下文获取
      }
    };

    // 乐观更新：立即添加到列表
    this.setState(prevState => ({
      comments: this.sortCommentsByServerTime([...prevState.comments, optimisticComment])
    }));
    
    try {
      const response = await postComment(this.props.orderId, content.trim(), this.props.currentStage);
      
      // 检查响应拦截器是否已经处理了错误（返回null表示已处理）
      if (response === null) {
        // 响应拦截器已经显示了错误信息，只需要清理乐观更新
        this.setState(prevState => ({
          comments: prevState.comments.filter(comment => comment.id !== optimisticComment.id)
        }));
        return;
      }
      
      // 检查返回的通用状态
      if (response && response.resp_common && response.resp_common.ret === 0) {
        // 如果有完整的评论对象，直接使用
        if (response.comment) {
          this.setState(prevState => {
            const commentsWithoutOptimistic = prevState.comments.filter(
              comment => comment.id !== optimisticComment.id
            );
            
            return {
              comments: this.sortCommentsByServerTime([...commentsWithoutOptimistic, response.comment])
            };
          });
          
          message.success('评论发表成功');
          return;
        }

        // 验证服务器返回的兼容字段是否有效
        const isValidCommentId = response.comment_id && response.comment_id !== "0";
        const isValidTimestamp = response.created_at && response.created_at !== "0001-01-01 00:00:00";
        
        if (!isValidCommentId || !isValidTimestamp) {
          console.warn('服务器返回的评论数据无效:', {
            comment_id: response.comment_id,
            created_at: response.created_at
          });
          
          // 如果服务器数据无效，重新加载评论列表以获取最新数据
          this.setState(prevState => ({
            comments: prevState.comments.filter(comment => comment.id !== optimisticComment.id)
          }));
          
          // 延迟重新加载，确保服务器数据已更新
          setTimeout(() => {
            this.loadComments(0, false);
          }, 1000);
          
          message.success('评论发表成功');
          return;
        }
        
        // 发表成功后，用服务器返回的兼容字段创建最终评论对象
        this.setState(prevState => {
          const commentsWithoutOptimistic = prevState.comments.filter(
            comment => comment.id !== optimisticComment.id
          );
          
          // 使用服务器返回的兼容字段创建最终评论对象
          const finalComment = {
            id: response.comment_id,
            content: content.trim(), // 保留用户输入的内容
            created_at: response.created_at, // 使用服务器时间
            author: optimisticComment.author // 保留本地用户信息
          };
          
          return {
            comments: this.sortCommentsByServerTime([...commentsWithoutOptimistic, finalComment])
          };
        });
        
        message.success('评论发表成功');
      } else {
        // 服务器返回错误状态，但响应拦截器没有处理（不应该发生）
        this.setState(prevState => ({
          comments: prevState.comments.filter(comment => comment.id !== optimisticComment.id)
        }));
        message.error('评论发表失败，请稍后重试');
      }
    } catch (error) {
      console.error('发表评论失败:', error);
      
      // 发表失败，移除乐观评论
      this.setState(prevState => ({
        comments: prevState.comments.filter(comment => comment.id !== optimisticComment.id)
      }));
      
      // 只有在非业务错误时才显示错误信息（如网络错误）
      if (!error.message || !error.message.includes('后台错误')) {
        message.error('网络错误，请稍后重试');
      }
    } finally {
      this.setState({ isSubmitting: false });
    }
  };

  /**
   * 处理加载更多评论
   */
  handleLoadMore = async () => {
    if (this.state.isLoading || !this.state.pagination.hasMore) return; // 防止重复加载

    try {
      // 使用下一个游标加载更多数据
      const nextCursor = this.state.pagination.nextCursor;
      if (nextCursor) {
        await this.loadComments(nextCursor, true);
      }
    } catch (error) {
      console.error('加载更多评论失败:', error);
      this.setState({ 
        error: '加载更多评论失败，请稍后重试',
        isLoading: false 
      });
    }
  };

  render() {
    const { canComment, orderId } = this.props;
    const { comments, pagination, isLoading, isSubmitting, error } = this.state;

    // 当orderId为空时显示加载状态
    if (!orderId) {
      return (
        <div className="comment-section-container">
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: '#999',
            fontSize: '14px'
          }}>
            正在加载工单信息...
          </div>
        </div>
      );
    }

    return (
      <div className="comment-section-container">
        {error && (
          <div style={{ 
            color: 'red', 
            padding: '12px',
            backgroundColor: '#fff2f0',
            borderBottom: '1px solid #ffccc7'
          }}>
            {error}
          </div>
        )}
        
        {/* 评论列表区域 */}
        <div className="comment-list-wrapper">
          <CommentList
            comments={comments}
            hasMore={pagination.hasMore}
            isLoading={isLoading}
            onLoadMore={this.handleLoadMore}
          />
        </div>
        
        {/* 评论输入框区域 */}
        <div className="comment-input-wrapper">
          <CommentInput
            isDisabled={!canComment || !orderId}
            isSubmitting={isSubmitting}
            onSubmit={this.handlePostComment}
          />
        </div>
      </div>
    );
  }
} 