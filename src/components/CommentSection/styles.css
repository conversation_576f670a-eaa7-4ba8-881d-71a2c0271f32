/* 评论区专属样式 - 迭代二 */

.comment-section-container {
  display: flex;
  flex-direction: column;
  height: 500px; /* 保持原有高度 */
  background-color: #fff;
}

/* 评论列表容器 - 可滚动区域 */
.comment-list-wrapper {
  flex: 1 1 auto;
  overflow: hidden; /* 让内部元素处理滚动 */
  padding: 16px;
  background-color: #fff;
}

/* 评论输入区容器 - 固定底部 */
.comment-input-wrapper {
  flex-shrink: 0; /* 关键：防止被压缩 */
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
} 