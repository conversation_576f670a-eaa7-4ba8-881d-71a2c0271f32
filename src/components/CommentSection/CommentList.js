import React, { useRef, useLayoutEffect, useEffect, useState } from 'react';
import { Avatar, List, Typography, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const { Text } = Typography;

/**
 * CommentList - 评论列表展示组件
 * 纯展示组件，负责渲染评论列表和懒加载滚动监听
 */
const CommentList = ({ comments, hasMore, isLoading, onLoadMore }) => {
  const listContainerRef = useRef(null);
  const sentinelRef = useRef(null);

  // 记录上一次评论数组，用于判断是新增评论还是加载历史评论
  const [prevComments, setPrevComments] = useState([]);
  
  /**
   * 自动滚动到底部 - 仅在首次加载或新增评论时执行
   * 使用useLayoutEffect确保在浏览器绘制前完成
   */
  useLayoutEffect(() => {
    const isFirstLoad = prevComments.length === 0 && comments.length > 0;
    
    // 检查是否是加载历史评论（新评论添加在数组前面）
    const isLoadingHistory = comments.length > prevComments.length && 
                            prevComments.length > 0 && 
                            comments[0]?.id !== prevComments[0]?.id;
    
    // 检查是否是新增评论（新评论添加在数组末尾）
    const isNewComment = comments.length > prevComments.length && 
                        prevComments.length > 0 && 
                        comments[comments.length - 1]?.id !== prevComments[prevComments.length - 1]?.id;
    
    if (listContainerRef.current) {
      if (isLoadingHistory) {
        // 历史评论加载时，保持滚动位置
        // 计算新加载的评论的高度总和
        const newCommentsCount = comments.length - prevComments.length;
        
        // 等待DOM更新后再调整滚动位置
        setTimeout(() => {
          if (listContainerRef.current) {
            // 获取每个新评论项的高度
            let newCommentsHeight = 0;
            for (let i = 0; i < newCommentsCount; i++) {
              const commentElement = listContainerRef.current.querySelector(`[data-comment-id="${comments[i].id}"]`);
              if (commentElement) {
                newCommentsHeight += commentElement.offsetHeight;
              }
            }
            
            // 调整滚动位置，保持用户看到的内容不变
            listContainerRef.current.scrollTop = newCommentsHeight;
          }
        }, 0);
      } else if (isFirstLoad || isNewComment) {
        // 首次加载或新增评论时滚动到底部
        listContainerRef.current.scrollTop = listContainerRef.current.scrollHeight;
      }
    }
    
    // 更新上一次评论数组
    setPrevComments([...comments]);
  }, [comments]); // 依赖项是评论数组

  /**
   * 设置IntersectionObserver监听器 - 实现懒加载
   */
  useEffect(() => {
    // 创建一个标记，用于防止同一滚动事件中多次触发加载
    let isLoadingTriggered = false;
    
    const observer = new IntersectionObserver(
      (entries) => {
        // 只有当哨兵元素进入视口，且有更多数据，且不在加载中，且未触发过加载时才执行
        if (entries[0].isIntersecting && hasMore && !isLoading && !isLoadingTriggered) {
          // 设置标记，防止同一滚动事件中重复触发
          isLoadingTriggered = true;
          
          // 暂时取消观察，防止多次触发
          const currentSentinel = sentinelRef.current;
          if (currentSentinel) {
            observer.unobserve(currentSentinel);
          }
          
          // 触发加载更多
          onLoadMore();
          
          // 延迟重新开始观察，确保加载请求已经发出
          setTimeout(() => {
            // 重置标记
            isLoadingTriggered = false;
            
            // 如果哨兵元素仍然存在且有更多数据，重新开始观察
            if (currentSentinel && hasMore) {
              observer.observe(currentSentinel);
            }
          }, 300); // 300ms延迟，避免频繁触发
        }
      },
      { 
        root: listContainerRef.current, // 使用列表容器作为观察的根
        rootMargin: '20px', // 提前20px触发加载
        threshold: 0.1 // 只有当至少10%的目标元素可见时才触发
      }
    );

    const currentSentinel = sentinelRef.current;
    if (currentSentinel && hasMore && !isLoading) {
      observer.observe(currentSentinel);
    }

    return () => {
      if (currentSentinel) {
        observer.unobserve(currentSentinel);
      }
    };
  }, [onLoadMore, hasMore, isLoading]); // 依赖项

  /**
   * 格式化时间显示
   */
  const formatTime = (timeString) => {
    const now = new Date();
    const commentTime = new Date(timeString);
    const diffMs = now - commentTime;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

    if (diffHours < 24) {
      // 24小时以内显示多少小时前
      if (diffHours < 1) {
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        if (diffMinutes < 1) {
          return '刚刚';
        } else {
          return `${diffMinutes}分钟前`;
        }
      } else {
        return `${diffHours}小时前`;
      }
    } else {
      // 24小时以前显示具体日期和时间
      return commentTime.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  /**
   * 生成用户头像文字 - 与后端逻辑保持一致
   */
  const generateAvatarText = (name) => {
    if (!name || !name.trim()) {
      return '未'; // 未知用户的默认头像
    }
    
    const trimmedName = name.trim();
    if (trimmedName.length === 2) {
      return trimmedName; // 两个字显示全名
    } else if (trimmedName.length >= 3) {
      return trimmedName.slice(-2); // 三个字及以上显示后两个字
    } else {
      return trimmedName; // 一个字显示全名
    }
  };

  /**
   * 渲染单条评论
   */
  const renderCommentItem = (comment) => {
    const { author, content, created_at } = comment;
    
    // 过滤空内容的评论
    if (!content || !content.trim()) {
      return null;
    }
    
    return (
      <List.Item key={comment.id} data-comment-id={comment.id}>
        <List.Item.Meta
          avatar={
            <Avatar 
              style={{ 
                backgroundColor: '#1890ff',
                color: '#fff',
                fontSize: '14px',
                fontWeight: 'bold'
              }}
              size="default"
            >
              {author?.avatar_text || generateAvatarText(author?.name)}
            </Avatar>
          }
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Text strong>{author?.name || '未知用户'}</Text>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {formatTime(created_at)}
              </Text>
            </div>
          }
          description={
            <div style={{ 
              whiteSpace: 'pre-wrap', 
              wordBreak: 'break-word',
              marginTop: '4px' 
            }}>
              {content}
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <div style={{ height: '100%' }}>
      {/* 滚动容器 - 恢复原有的美观样式 */}
      <div
        ref={listContainerRef}
        style={{
          height: '100%',
          overflowY: 'auto',
          paddingRight: '8px',
          backgroundColor: '#fff',
          border: '1px solid #f0f0f0',
          borderRadius: '4px',
          padding: '8px'
        }}
      >
        {/* 顶部加载指示器 */}
        {isLoading && hasMore && (
          <div style={{ 
            textAlign: 'center', 
            padding: '16px 0',
            borderBottom: '1px solid #f0f0f0'
          }}>
            <Spin 
              indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />} 
              size="small" 
            />
            <Text type="secondary" style={{ marginLeft: '8px' }}>
              加载更多评论...
            </Text>
          </div>
        )}
        
        {/* 哨兵元素 - 用于IntersectionObserver监听 */}
        {hasMore && <div ref={sentinelRef} style={{ height: '1px' }} />}
        
        {/* 评论列表 */}
        {comments.length === 0 && !isLoading ? (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: '#999',
            fontSize: '14px'
          }}>
            暂无评论，快来发表第一条吧！
          </div>
        ) : (
          <List
            itemLayout="horizontal"
            dataSource={comments.filter(comment => comment.content && comment.content.trim())}
            renderItem={renderCommentItem}
            style={{ marginBottom: '0' }}
          />
        )}
        
        {/* 没有更多数据的提示 */}
        {!hasMore && comments.length > 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '16px 0',
            borderTop: '1px solid #f0f0f0'
          }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              没有更多评论了
            </Text>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentList; 