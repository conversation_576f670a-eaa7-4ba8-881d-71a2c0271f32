import React, { Component } from 'react';
import { Input, Button, Typography } from 'antd';
import { SendOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Text } = Typography;

/**
 * CommentInput - 评论输入框展示组件
 * 纯展示组件，提供受控的文本输入框和发表按钮
 */
export default class CommentInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputValue: '',
      charCount: 0,
    };
  }

  /**
   * 处理输入变化
   */
  handleInputChange = (event) => {
    const value = event.target.value;
    
    // 长度限制：150个字符
    if (value.length <= 150) {
      this.setState({
        inputValue: value,
        charCount: value.length,
      });
    }
  };

  /**
   * 处理提交
   */
  handleSubmit = () => {
    const { inputValue } = this.state;
    const trimmedValue = inputValue.trim();
    
    if (!trimmedValue) {
      // 在父组件中已经有错误提示，这里直接返回
      return;
    }

    if (trimmedValue.length > 150) {
      // 在父组件中已经有错误提示，这里直接返回
      return;
    }

    // 调用父组件传递的回调
    this.props.onSubmit(trimmedValue);
    
    // 清空输入框
    this.setState({
      inputValue: '',
      charCount: 0,
    });
  };

  /**
   * 处理键盘事件
   */
  handleKeyDown = (event) => {
    // Ctrl + Enter 或 Cmd + Enter 快速提交
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      this.handleSubmit();
    }
  };

  render() {
    const { isDisabled, isSubmitting } = this.props;
    const { inputValue, charCount } = this.state;
    
    // 按钮禁用条件：输入为空、超长、组件禁用、提交中
    const isButtonDisabled = 
      isDisabled || 
      isSubmitting || 
      !inputValue.trim() || 
      charCount > 150;

    return (
      <div>
        <div style={{ position: 'relative' }}>
          <TextArea
            placeholder={isDisabled ? '工单已完结，无法添加评论' : '添加评论...'}
            value={inputValue}
            onChange={this.handleInputChange}
            onKeyDown={this.handleKeyDown}
            disabled={isDisabled}
            rows={3}
            maxLength={150}
            style={{ 
              resize: 'none',
              marginBottom: '12px',
              paddingRight: '50px' // 给字符计数留出空间
            }}
          />
          <span style={{ 
            position: 'absolute', 
            right: '12px', 
            bottom: '24px', // 调整位置到输入框内部
            fontSize: '12px', 
            color: charCount > 150 ? '#ff4d4f' : '#999',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            padding: '2px 6px',
            borderRadius: '4px',
            pointerEvents: 'none',
            zIndex: 10,
            border: '1px solid #e8e8e8'
          }}>
            {charCount}/150
          </span>
        </div>
        
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center' 
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {!isDisabled && 'Ctrl + Enter 快速发表'}
          </Text>
          
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={this.handleSubmit}
            disabled={isButtonDisabled}
            loading={isSubmitting}
            size="small"
          >
            {isSubmitting ? '发表中...' : '发表'}
          </Button>
        </div>
      </div>
    );
  }
} 