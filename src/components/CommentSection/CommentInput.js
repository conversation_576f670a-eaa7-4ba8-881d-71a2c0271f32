import React, { Component } from 'react';
import { Input, Button, Typography } from 'antd';
import { SendOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Text } = Typography;

/**
 * CommentInput - 评论输入框展示组件
 * 纯展示组件，提供受控的文本输入框和发表按钮
 */
export default class CommentInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputValue: '',
      charCount: 0,
    };
  }

  /**
   * 处理输入变化
   */
  handleInputChange = (event) => {
    const value = event.target.value;
    
    // 长度限制：150个字符
    if (value.length <= 150) {
      this.setState({
        inputValue: value,
        charCount: value.length,
      });
    }
  };

  /**
   * 处理提交
   */
  handleSubmit = () => {
    const { inputValue } = this.state;
    const trimmedValue = inputValue.trim();
    
    if (!trimmedValue) {
      // 在父组件中已经有错误提示，这里直接返回
      return;
    }

    if (trimmedValue.length > 150) {
      // 在父组件中已经有错误提示，这里直接返回
      return;
    }

    // 调用父组件传递的回调
    this.props.onSubmit(trimmedValue);
    
    // 清空输入框
    this.setState({
      inputValue: '',
      charCount: 0,
    });
  };

  /**
   * 处理键盘事件
   */
  handleKeyDown = (event) => {
    // Ctrl + Enter 或 Cmd + Enter 快速提交
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      this.handleSubmit();
    }
  };

  render() {
    const { isDisabled, isSubmitting } = this.props;
    const { inputValue, charCount } = this.state;
    
    // 按钮禁用条件：输入为空、超长、组件禁用、提交中
    const isButtonDisabled = 
      isDisabled || 
      isSubmitting || 
      !inputValue.trim() || 
      charCount > 150;

    return (
      <div>
        <div style={{ position: 'relative' }}>
          <TextArea
            placeholder={isDisabled ? '工单已完结，无法添加评论' : '添加评论...'}
            value={inputValue}
            onChange={this.handleInputChange}
            onKeyDown={this.handleKeyDown}
            disabled={isDisabled}
            rows={3}
            maxLength={150}
            style={{
              resize: 'none',
              marginBottom: '12px',
              paddingRight: '60px', // 给字符计数留出更多空间
              borderRadius: '6px', // 与页面风格一致的圆角
              border: '1px solid #e2e8f0', // 与页面一致的边框颜色
              fontSize: '14px'
            }}
          />
          <span style={{
            position: 'absolute',
            right: '12px',
            bottom: '24px', // 调整位置到输入框内部右下角
            fontSize: '12px',
            color: charCount > 150 ? '#ef4444' : '#6b7280', // 使用页面风格的颜色
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            padding: '2px 6px',
            borderRadius: '4px',
            pointerEvents: 'none',
            zIndex: 10,
            border: '1px solid #e2e8f0',
            fontWeight: '500'
          }}>
            {charCount}/150
          </span>
        </div>

        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Text type="secondary" style={{
            fontSize: '12px',
            color: '#6b7280' // 与页面风格一致的文字颜色
          }}>
            {!isDisabled && 'Ctrl + Enter 快速提交'}
          </Text>

          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={this.handleSubmit}
            disabled={isButtonDisabled}
            loading={isSubmitting}
            size="small"
            style={{
              borderRadius: '6px', // 与页面风格一致的圆角
              fontWeight: '500',
              backgroundColor: '#0f172a', // 与页面按钮风格一致
              borderColor: '#0f172a'
            }}
          >
            {isSubmitting ? '发表中...' : '发表评论'}
          </Button>
        </div>
      </div>
    );
  }
} 