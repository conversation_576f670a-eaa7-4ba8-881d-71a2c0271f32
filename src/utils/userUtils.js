import Cookies from 'js-cookie';

/**
 * 用户信息工具函数
 */

/**
 * 从JWT token中解析用户信息
 * @param {string} token - JWT token
 * @returns {Object|null} 解析出的用户信息，包含 UserEmail 和 UserName
 */
const parseJWTToken = (token) => {
  try {
    if (!token) return null;
    
    // JWT token 由三部分组成，用.分隔：header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    // 解析payload部分（第二部分）
    const payload = parts[1];
    
    // Base64 URL decode
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    
    // 解析JSON
    const userInfo = JSON.parse(decoded);
    
    return userInfo;
  } catch (error) {
    console.error('解析JWT token失败:', error);
    return null;
  }
};

/**
 * 获取当前登录用户的信息
 * @returns {Object} 用户信息对象
 */
export const getCurrentUser = () => {
  // 从Cookie获取用户邮箱
  const userEmail = Cookies.get('user_email');
  
  // 从Cookie获取token并解析用户名
  const token = Cookies.get('token');
  const tokenInfo = parseJWTToken(token);
  
  // 优先使用token中的用户名，如果没有则从邮箱提取
  let userName = tokenInfo?.UserName;
  
  if (!userName && userEmail) {
    // 如果token中没有用户名，从邮箱中提取用户名部分
    userName = userEmail.split('@')[0];
  }
  
  return {
    email: userEmail || '',
    name: userName || '未知用户',
    fullInfo: tokenInfo
  };
};

/**
 * 获取当前用户的显示名称
 * @returns {string} 用户显示名称
 */
export const getCurrentUserName = () => {
  const user = getCurrentUser();
  return user.name;
};

/**
 * 获取当前用户的邮箱
 * @returns {string} 用户邮箱
 */
export const getCurrentUserEmail = () => {
  const user = getCurrentUser();
  return user.email;
};
